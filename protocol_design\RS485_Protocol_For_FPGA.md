# RS485 PC–FPGA 通信协议 (MVP版本)
#### 2025年1月版本

本文档定义 PC 端 RS485 驱动与 FPGA 设备通信的精简协议。为 MVP 开发，要求工作量最小化，协议简洁明了。

## 支持的指令范围
- 系统指令：S001（设置从机地址）
- 用户指令：U001–U006（设备配置参数）
- 12字节载荷：前4字节ASCII指令键，后8字节数据（little-endian）
- **双向通信**：PC发送指令，FPGA必须返回确认
- FRAM持久化存储

## 1. 物理层和链路层
- **传输介质**：RS485 (2线 A/B)
- **波特率**：9600 bps（默认）
- **帧结构**：16字节固定长度
  - 帧头：1字节 = 0xAA
  - ID字节：1字节 = [功能码(3位) | 从机地址(5位)]
    - PC发送指令：0b111 (Assign Data)
    - FPGA返回确认：0b010 (Response to Assign)
    - 从机地址范围：1..31；广播使用 0x00
  - 载荷：12字节（见第2节）
  - CRC8：1字节 — 多项式 0x97，初值 0x00，覆盖13字节（ID + 载荷）
  - 帧尾：1字节 = 0x0D

**重要**：CRC8只覆盖13字节（ID字节 + 12字节载荷），不包括帧头和帧尾。

## 2. 载荷格式 (12字节)
- 字节 [0..3]：ASCII指令键，如 'S','0','0','1' 或 'U','0','0','1'
- 字节 [4..11]：8字节二进制数据，**little-endian 编码**
  - **单个16位参数**：使用字节 [4..5]；字节 [6..11] 填零
  - **双16位参数**（如GPIO通道+使能）：使用字节 [4..5] 和 [6..7]；字节 [8..11] 填零
  - **保持12字节载荷**：为协议统一性，始终使用12字节载荷

**数据类型说明**：当前所有参数值都较小，16位整数足够表示。如需浮点数，采用定点映射（如：实际值×1000存储为整数）。

## 3. 指令集 (S001, U001–U006)

PC发送指令使用功能码 0b111 (Assign Data)。FPGA收到指令后必须返回确认，使用功能码 0b010 (Response to Assign)。

### 系统配置指令
- **S001 — 设置从机地址**
  - 键值：'S','0','0','1'
  - 寻址：广播 (ID地址位 = 0)
  - 载荷 [4..5]：uint16 新地址 (1..31)，little-endian；[6..11] = 0
  - 动作：更新设备运行时从机地址并存储到FRAM

### 用户配置指令
- **U001 — 设置SEL阈值电流 (mA)**
  - 键值：'U','0','0','1'
  - 载荷 [4..5]：uint16 阈值_mA (40..500)；[6..11] = 0
  - 动作：存储到FRAM；应用到SEL逻辑

- **U002 — 设置SEL最大幅值 (mA)**
  - 键值：'U','0','0','2'
  - 载荷 [4..5]：uint16 幅值_mA (1000..2000)；[6..11] = 0
  - 动作：存储到FRAM；应用到SEL逻辑

- **U003 — 设置SEL检测次数**
  - 键值：'U','0','0','3'
  - 载荷 [4..5]：uint16 次数 (1..5)；[6..11] = 0
  - 动作：存储到FRAM；连续检测次数达到后触发功率循环

- **U004 — 设置功率循环持续时间 (ms)**
  - 键值：'U','0','0','4'
  - 载荷 [4..5]：uint16 持续时间_ms，必须为 {200, 400, 600, 800, 1000} 之一；[6..11] = 0
  - 动作：存储到FRAM；用于功率循环控制

- **U005 — 配置GPIO输入通道**
  - 键值：'U','0','0','5'
  - 载荷 [4..5]：uint16 通道 (0或1)
  - 载荷 [6..7]：uint16 使能 (0=禁用, 1=启用)；[8..11] = 0
  - 动作：存储到FRAM；应用到GPIO输入配置

- **U006 — 配置GPIO输出通道**
  - 键值：'U','0','0','6'
  - 载荷 [4..5]：uint16 通道 (0或1)
  - 载荷 [6..7]：uint16 使能 (0=禁用, 1=启用)；[8..11] = 0
  - 动作：存储到FRAM；应用到GPIO输出配置

**GPIO功能说明**：GPIO的Enable/Disable指GPIO功能的开关，由FPGA软件控制数字输入输出功能。

**Little-endian组装**：如HDL串行读取字节，16位值组装为 v = b0 + (b1<<8)。

## 4. CRC8 计算 (多项式 0x97)
- **覆盖范围**：13字节（ID字节 + 12字节载荷）
- **初始值**：0x00
- **多项式**：0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1
- **算法**（LSB优先）：
  ```
  对每个输入字节：
    对8位：
      mix = (crc ^ byte) & 0x01
      crc >>= 1
      if (mix) crc ^= 0x97
      byte >>= 1
  ```

PC驱动使用此算法，FPGA必须实现相同算法以确保兼容性。

## 5. 通信流程

```
PC端                           FPGA端
  |                              |
  |------ 发送指令帧 ------>     |
  |     (功能码 0b111)           |
  |                              |-- 接收16字节
  |                              |-- 验证帧头(0xAA)和帧尾(0x0D)
  |                              |-- 计算并验证CRC8
  |                              |
  |                              |-- CRC错误？
  |   <---- 重发请求 ------------|   是：发送重发请求
  |                              |
  |                              |-- CRC正确？
  |                              |   是：检查地址
  |                              |
  |                              |-- 地址匹配？
  |                              |   是：执行指令
  |                              |   否：忽略
  |                              |
  |   <---- 返回确认帧 ---------|-- 发送确认帧
  |     (功能码 0b010)           |   (包含收到的指令内容)
  |                              |
  |-- 验证返回内容               |
  |-- 内容正确：完成             |
  |-- 内容错误或超时：重发       |
```

### 关键流程说明：
1. **帧同步**：所有设备监听RS485总线，收到帧头(0xAA)才开始读取
2. **完整接收**：读取完整16字节，确认帧尾(0x0D)
3. **CRC验证**：计算13字节CRC，不匹配则发送重发请求
4. **地址过滤**：CRC正确后检查地址，只有匹配的设备执行指令
5. **确认响应**：执行指令后，FPGA必须发送确认帧给PC
6. **内容校验**：PC验证返回的指令内容，确保通信正确

## 6. FRAM存储
- **用途**：持久化存储所有配置参数
- **地址分配**：由FPGA端（Junkai）自行决定地址映射
- **存储内容**：
  - S001 从机地址
  - U001-U006 所有用户配置参数
- **数据格式**：little-endian，与协议保持一致
- **上电加载**：FPGA启动时从FRAM读取所有配置参数

## 7. 错误处理
- **CRC错误**：FPGA发送重发请求（功能码 0b000）
- **地址不匹配**：静默忽略（不发送任何响应）
- **参数超出范围**：静默忽略或发送错误响应
- **超时处理**：PC端超时后重发指令

## 8. 实现要点
1. **UART接收**：解析16字节帧，确认帧头和帧尾
2. **CRC计算**：对13字节（ID+载荷）计算CRC8
3. **功能码处理**：
   - 接收：0b111 (Assign Data)
   - 发送：0b010 (Response to Assign)
4. **地址匹配**：提取ID字节低5位，与本机地址比较
5. **载荷解析**：提取ASCII键值和二进制数据
6. **FRAM操作**：立即存储配置参数
7. **响应发送**：构造确认帧并发送

本协议为MVP版本，保持最小复杂度，确保PC和FPGA端开发工作量最小化。
